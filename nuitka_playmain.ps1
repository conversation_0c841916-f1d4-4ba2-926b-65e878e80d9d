# webdisplay模块用了cv2，所以要包含
# ！！编译后记得要拷贝：yaml、html模板、js、css、几个txt、ttf、otf、ttc共计11个文件到exe目录！包括：
# ✅  config_play.yaml - 主配置文件、config_getorder.yaml
# ✅  Config_livecam.yaml - 摄像头配置
# ✅  webdisplay_template.html - HTML模板
# ✅  msyhbd.ttc - 主要字体文件
# ✅  segoe-ui-emoji.ttf - 表情字体
# ✅  DouyinSansBold.otf - 备用字体
# ✅  托玩家列表.txt - 虚拟玩家列表
# ✅  直播词提示.txt - 提示词文件
# ✅ 两个js文件和其他配置和资源文件
nuitka --standalone `
  --python-flag=no_site `
  --show-progress `
  --show-memory `
  --assume-yes-for-downloads `
  --enable-plugin=tk-inter `
  --disable-plugin=pywebview `
  --include-package=yaml `
  --include-package=numpy `
  --include-package=playwright `
  --include-package=obsws_python `
  --include-package=requests `
  --include-package=cv2 `
  --include-module=play_init `
  --include-module=Play_db `
  --include-module=Play_obs `
  --include-module=play_db_sync `
  --include-module=play_processing `
  --include-module=Play_receiveMSG `
  --include-module=Play_rec_detect `
  --include-module=Play_move_client `
  --include-module=Play_webdisplay `
  --include-module=Play_virtual_player `
  --include-module=Play_request_guard `
  --include-module=play_displayer `
  --include-module=play_cleanup `
  --include-module=tkinter `
  --include-module=tkinter.ttk `
  --include-module=tkinter.font `
  --include-module=asyncio `
  --include-module=websockets `
  --include-module=websockets.asyncio `
  --include-module=websockets.asyncio.server `
  --include-module=socketserver `
  --include-module=http.server `
  --include-module=sqlite3 `
  --include-module=copy `
  --include-module=queue `
  --include-module=threading `
  --include-module=logging `
  --include-module=random `
  --include-module=json `
  --include-module=signal `
  --include-module=hashlib `
  --include-module=pathlib `
  --include-module=playwright `
  --include-module=playwright.async_api `
  --include-module=multiprocessing `
  play_main.py