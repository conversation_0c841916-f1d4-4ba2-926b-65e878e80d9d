# 浏览器配置
browser:
  # 是否使用已登录的用户数据。打开哪个网页，是代码里写死的
  use_user_data: true
  # 用户数据目录，仅当 use_user_data 为 true 时有效（注意双斜线，chrome里输入chrome://version/，查个人资料路径）
  user_data_dir: "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default"
  # 是否启用 headless 模式（无界面），最好为否，因为可能要人工登录、去除广告等
  headless: false

# 订单查询配置，关键！▉正常工作是查询“买家已付款+目标商品ID”▉
order_query:
  # ▉订单状态筛选条件，可选值包括："全部"，"买家已付款", "卖家已发货", "交易成功", "等待买家付款" 等
  order_status: "买家已付款"
  # ▉商品ID筛选，留空则不按商品ID筛选;926293477798 是补差价，925874048478 是智能设备
  product_id: "925874048478"
  # 新增：最大翻页数，设为1则不翻页，设置合理的数值避免无限循环
  max_pages: 5

# 操作延迟配置（单位：秒）
delays:
  # 页面导航后延迟
  navigation:
    min: 1.0
    max: 2.0
  # 点击操作后延迟
  click:
    min: 0.3
    max: 0.6
  # 输入操作后延迟
  input:
    min: 0.2
    max: 0.6
  # 悬停操作后延迟
  hover:
    min: 0.1
    max: 0.3
  # 搜索后等待结果延迟
  search_wait:
    min: 1.4
    max: 2.5
  # 处理订单数据间隔延迟 (原 process_order，用于主要循环间歇)
  process_order:
    min: 0.1
    max: 0.25
  # 登录后操作延迟
  after_login:
    min: 0.8
    max: 1.3
  # 关闭弹窗后延迟
  after_popup:
    min: 0.4
    max: 0.8
  # Playwright 的 slow_mo 参数 (毫秒)
  slow_mo: 50

  # --- 新增的细化延迟配置 ---
  # 手动处理滑块验证码后的等待
  after_manual_captcha_input:
    min: 2.8
    max: 3.2
  # 点击页面主体关闭可能的浮动通知后的延迟
  after_body_click_popup:
    min: 0.3
    max: 0.6
  # 检查登录状态前的初始等待
  login_check_initial_wait:
    min: 0.5
    max: 1.0
  # 初始页面加载后，处理完验证码后的等待
  post_initial_captcha_wait:
    min: 1.3
    max: 1.9
  # 等待订单状态选择器可见后的延迟
  after_order_status_selector_wait:
    min: 0.2
    max: 0.3
  # 点击订单状态下拉触发器后的延迟
  after_dropdown_trigger_click:
    min: 0.2
    max: 0.35
  # JS备选方法选择订单状态后的延迟
  after_js_dropdown_fallback:
    min: 0.3
    max: 0.5
  # 清空商品ID输入框后的短延迟
  after_pid_clear:
    min: 0.1
    max: 0.3
  # 处理完搜索按钮后的验证码后的延迟
  after_search_captcha_handled:
    min: 0.5
    max: 1.0
  # 在单个订单列表页面，处理每个订单项之间的延迟
  between_order_item_processing:
    min: 0.05
    max: 0.15
  # 独立运行时，脚本结束前的等待
  standalone_end_wait:
    min: 1.0
    max: 1.8
