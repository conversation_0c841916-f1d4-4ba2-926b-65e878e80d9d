import obsws_python as obs
import obsws_python  # 不加这句编译后会找不到模块
import yaml
import time
import logging
import threading
import datetime
import traceback
from typing import Dict, List, Any, Tuple

logger = logging.getLogger('play_obs')

class OBSController:
    def __init__(self, config=None):
        """初始化OBS控制器"""
        self.config = config or self._get_default_config()
        self.client = None
        self.connected = False
        self.lock = threading.Lock()
        self.last_update_time = 0
        
        # 获取配置 - 只从obs配置中读取
        obs_config = self.config.get('obs', {})
        self.update_interval = obs_config.get('update_interval', 0.08)
        # 修改：移除单一特效源配置的支持，改为完全使用编号方案
        # self.congrats_video_source = obs_config.get('congrats_video_source', '抓中特效')
        self.congrats_duration = obs_config.get('congrats_duration', 2)
        
        # 缓存设置
        self.source_settings = {}
        self.cached_scene_name = None
        self.cached_scene_item_ids: Dict[str, int] = {}
        self.visibility_states: Dict[str, bool] = {}
        
        # 背景视频动画控制
        self._back_video_anim_thread = None
        self._back_video_anim_stop_event = threading.Event()
        
        self.connect_to_obs()

    def _get_default_config(self):
        """获取默认配置"""
        return {
            'obs': {'host': 'localhost', 'port': 4455, 'password': '',
                   'congrats_video_source': '抓中特效'}
        }

    def connect_to_obs(self):
        """连接到OBS WebSocket"""
        obs_config = self.config.get('obs', {})
        host = obs_config.get('host', 'localhost')
        port = obs_config.get('port', 4455)
        password = obs_config.get('password', '')
        
        try:
            logger.info(f"正在连接到OBS WebSocket (host={host}, port={port})...")
            self.client = obs.ReqClient(host=host, port=port, password=password, timeout=5)
            self.connected = True
            logger.info("成功连接到OBS WebSocket")
            
            # 缓存场景信息
            try:
                response = self.client.get_current_program_scene()
                # 修正: 使用正确的属性访问方式
                current_scene = response.current_program_scene_name
                self.cached_scene_name = current_scene
                logger.info(f"当前场景: {current_scene}")
                
                # 修正: 使用正确的属性访问方式
                scene_items_response = self.client.get_scene_item_list(current_scene)
                scene_items = scene_items_response.scene_items

                # --- 新增：收集所有祝贺源和未抓中特效源 ---
                sources_to_cache = []
                i = 1
                while True:
                    key = f'congrats_video_source{i}'
                    if key in obs_config:
                        sources_to_cache.append(obs_config[key])
                        i += 1
                    else:
                        break
                if not sources_to_cache:
                    # 兼容旧配置
                    legacy_source = obs_config.get('congrats_video_source')
                    if legacy_source:
                        sources_to_cache.append(legacy_source)
                        logger.warning(f"检测到使用旧的特效源配置 '{legacy_source}'，建议更新到编号方案 (congrats_video_source1, congrats_video_source2, ...)")
                # 加入未抓中特效源
                missed_sound = obs_config.get('missed_sound_source', '')
                if missed_sound:
                    sources_to_cache.append(missed_sound)
                # --- end ---

                for name in sources_to_cache:
                    if not name:
                        continue
                    
                    source_found = False
                    for item in scene_items:
                        if item['sourceName'] == name:
                            self.cached_scene_item_ids[name] = item['sceneItemId']
                            self.visibility_states[name] = item['sceneItemEnabled']
                            source_found = True
                            break
                    
                    if not source_found:
                        logger.warning(f"未在场景 '{current_scene}' 中找到源 '{name}'")
                    else:
                        logger.debug(f"已缓存源 '{name}' 的场景项ID")
                
                logger.info(f"完成源缓存，已缓存 {len(self.cached_scene_item_ids)} 个源的场景项ID")
                
            except Exception as e:
                logger.warning(f"缓存源信息时出错: {e}")
        except Exception as e:
            logger.error(f"连接到OBS WebSocket失败: {e}")
            self.connected = False
            self._clear_cache()
    
    def _clear_cache(self):
        """清除所有缓存"""
        self.cached_scene_name = None
        self.cached_scene_item_ids.clear()
        self.visibility_states.clear()
        self.source_settings.clear()
    
    """
    def show_congrats(self, player: str, item: str):
        #显示祝贺特效 - 只处理OBS视频显示
        logger.info(f"显示祝贺特效视频：玩家={player}, 物品={item}")
        
        if not self.connected:
            self.connect_to_obs()
            if not self.connected:
                return False
    
        lock_acquired = False
        try:
            lock_acquired = self.lock.acquire(timeout=3)
            if not lock_acquired:
                logger.warning("获取OBS锁超时，跳过祝贺视频显示")
                return False
            
            try:
                # 显示祝贺视频源
                self._update_source_visibility(self.congrats_video_source, True)
                
                # 设置定时器隐藏祝贺
                def hide_after_delay():
                    time.sleep(self.congrats_duration)
                    self.hide_congrats()
                
                hide_thread = threading.Thread(target=hide_after_delay, daemon=True)
                hide_thread.start()
                
                return True
                
            except Exception as e:
                logger.error(f"OBS祝贺视频显示操作失败: {e}")
                return False
                
        finally:
            if lock_acquired:
                self.lock.release()
    """
    def hide_congrats(self):
        """隐藏祝贺特效视频 - 修改为同时处理所有可能的特效源"""
        if not self.connected:
            return False
        
        lock_acquired = False
        try:
            lock_acquired = self.lock.acquire(timeout=3)
            if not lock_acquired:
                logger.warning("获取OBS锁超时！放弃隐藏祝贺视频特效")
                return False
            
            try:
                # 获取所有祝贺特效源名称
                obs_config = self.config.get('obs', {})
                sources_to_hide = []
                
                # 收集编号方案的源
                i = 1
                while True:
                    key = f'congrats_video_source{i}'
                    if key in obs_config:
                        sources_to_hide.append(obs_config[key])
                        i += 1
                    else:
                        break
                
                # 兼容旧配置
                if not sources_to_hide:
                    legacy_source = obs_config.get('congrats_video_source')
                    if legacy_source:
                        sources_to_hide.append(legacy_source)
                
                # 添加未抓中特效源
                missed_sound = obs_config.get('missed_sound_source', '')
                if missed_sound:
                    sources_to_hide.append(missed_sound)
                
                # 隐藏所有特效源
                for source_name in sources_to_hide:
                    if source_name:
                        self._update_source_visibility(source_name, False)
                
                return True
                
            except Exception as e:
                logger.error(f"隐藏祝贺视频特效时出错: {e}")
                return False
                
        finally:
            if lock_acquired:
                self.lock.release()
    
    def _update_source_visibility(self, source_name: str, visible: bool):
        """更新指定源的可见性，假设已持有锁"""
        if not source_name:
            return
            
        # 跳过相同状态
        if source_name in self.visibility_states and self.visibility_states[source_name] == visible:
            return
        
        try:
            scene = self.cached_scene_name
            if not scene:
                response = self.client.get_current_program_scene()
                # 修正: 使用正确的属性访问方式
                scene = response.current_program_scene_name
                self.cached_scene_name = scene
            
            scene_item_id = self.cached_scene_item_ids.get(source_name)
            if scene_item_id is None:
                # 修正: 使用正确的属性访问方式
                scene_items_response = self.client.get_scene_item_list(scene)
                scene_items = scene_items_response.scene_items
                
                for item in scene_items:
                    if item['sourceName'] == source_name:
                        scene_item_id = item['sceneItemId']
                        self.cached_scene_item_ids[source_name] = scene_item_id
                        break
                        
            if scene_item_id is None:
                logger.warning(f"在场景 '{scene}' 中未找到源 '{source_name}'")
                return
                
            self.client.set_scene_item_enabled(scene, scene_item_id, visible)
            self.visibility_states[source_name] = visible
            
        except Exception as e:
            logger.error(f"更新源 '{source_name}' 可见性时出错: {e}")
            self.connected = False
            self.cached_scene_item_ids.pop(source_name, None)
            self.visibility_states.pop(source_name, None)
    
    def _update_source_text(self, source_name: str, text: str):
        """更新指定文本源的内容 - 此方法不再需要"""
        logger.debug(f"OBSController._update_source_text called for {source_name}, but OBS text display is disabled.")

    def show_current_player(self, player_name: str):
        """显示当前正在游戏的玩家提示 - 此方法不再需要"""
        logger.debug(f"OBSController.show_current_player called for {player_name}, but OBS text display is disabled.")
        return True
                
    def hide_current_player(self):
        """隐藏当前玩家提示 - 此方法不再需要"""
        logger.debug("OBSController.hide_current_player called, but OBS text display is disabled.")
        return True

    def animate_back_video(self):
        """分步移动和缩放背景视频，支持中断和重启，参数热更新"""
        obs_cfg = self.config.get('obs', {})
        # 新增：功能开关判断
        if not obs_cfg.get('back_video_move_enabled', True):
            logger.info("背景视频动画功能未启用，跳过动画。")
            return
        source_name = obs_cfg.get('back_video_source', '背景视频')
        moveY_str = obs_cfg.get('back_video_moveY', '-50px')
        try:
            scale = float(obs_cfg.get('back_video_scale', 1.05))
        except Exception:
            scale = 1.05
        try:
            steps = int(obs_cfg.get('back_video_steps', 4))
        except Exception:
            steps = 4
        try:
            movetime = float(obs_cfg.get('back_video_movetime', 1))
        except Exception:
            movetime = 1
        try:
            dwell_time = float(obs_cfg.get('back_video_dwell_time', 2))
        except Exception:
            dwell_time = 2

        # 解析moveY
        try:
            moveY = int(str(moveY_str).replace('px', '').strip())
        except Exception:
            moveY = -50

        # 获取scene和sceneItemId
        if not self.connected:
            self.connect_to_obs()
            if not self.connected:
                logger.warning("无法连接OBS，动画中止")
                return

        scene = self.cached_scene_name
        scene_item_id = self.cached_scene_item_ids.get(source_name)
        if scene_item_id is None:
            try:
                scene_items = self.client.get_scene_item_list(scene).scene_items
                for item in scene_items:
                    if item['sourceName'] == source_name:
                        scene_item_id = item['sceneItemId']
                        self.cached_scene_item_ids[source_name] = scene_item_id
                        break
            except Exception as e:
                logger.error(f"获取背景视频sceneItemId失败: {e}")
                return

        # 获取初始transform
        try:
            resp = self.client.get_scene_item_transform(scene, scene_item_id)
            transform = resp.scene_item_transform
            # --- 新增：alignment修正逻辑 ---
            # 获取画布和源信息
            video_settings = self.client.get_video_settings()
            canvas_width = getattr(video_settings, 'base_width', 1920)
            canvas_height = getattr(video_settings, 'base_height', 1080)
            source_width = transform.get('sourceWidth', 0)
            source_height = transform.get('sourceHeight', 0)
            scale_x = transform.get('scaleX', 1.0)
            scale_y = transform.get('scaleY', 1.0)
            alignment = transform.get('alignment', 5)
            pos_x = transform.get('positionX', 0)
            pos_y = transform.get('positionY', 0)
            initial_scale_x = scale_x
            initial_scale_y = scale_y

            # alignment=5（中心点）时，pos_x/pos_y 就是中心点
            # alignment=8（下边缘中点）时，pos_x/pos_y 是下边缘中心点
            if alignment != 8:
                center_x = pos_x
                center_y = pos_y
                new_pos_x = canvas_width / 2
                new_pos_y = center_y + (source_height * scale_y) / 2
            else:
                new_pos_x = pos_x
                new_pos_y = pos_y

            initial_y = new_pos_y
        except Exception as e:
            logger.error(f"获取背景视频transform失败: {e}")
            return

        target_y = initial_y + moveY
        target_scale = scale
        step_sleep = movetime / steps if steps > 0 else 0.1

        def anim_worker():
            try:
                # 步进到目标
                for i in range(1, steps + 1):
                    if self._back_video_anim_stop_event.is_set():
                        return
                    ratio = i / steps
                    new_y = initial_y + (target_y - initial_y) * ratio
                    new_scale = initial_scale_x + (target_scale - initial_scale_x) * ratio
                    safe_transform = {k: v for k, v in transform.items() if not k.startswith('bounds')}
                    self.client.set_scene_item_transform(scene, scene_item_id, {
                        **safe_transform,
                        'alignment': 8,
                        'positionX': new_pos_x,
                        'positionY': new_y,
                        'scaleX': new_scale,
                        'scaleY': new_scale
                    })
                    time.sleep(step_sleep)
                # 停留
                t0 = time.time()
                while time.time() - t0 < dwell_time:
                    if self._back_video_anim_stop_event.is_set():
                        return
                    time.sleep(0.05)
                # 步进回初始
                for i in range(1, steps + 1):
                    if self._back_video_anim_stop_event.is_set():
                        return
                    ratio = i / steps
                    new_y = target_y + (initial_y - target_y) * ratio
                    new_scale = target_scale + (initial_scale_x - target_scale) * ratio
                    safe_transform = {k: v for k, v in transform.items() if not k.startswith('bounds')}
                    self.client.set_scene_item_transform(scene, scene_item_id, {
                        **safe_transform,
                        'alignment': 8,
                        'positionX': new_pos_x,
                        'positionY': new_y,
                        'scaleX': new_scale,
                        'scaleY': new_scale
                    })
                    time.sleep(step_sleep)
                # 最后强制回初始
                safe_transform = {k: v for k, v in transform.items() if not k.startswith('bounds')}
                self.client.set_scene_item_transform(scene, scene_item_id, {
                    **safe_transform,
                    'alignment': 8,
                    'positionX': new_pos_x,
                    'positionY': initial_y,
                    'scaleX': initial_scale_x,
                    'scaleY': initial_scale_y
                })
            except Exception as e:
                logger.error(f"背景视频动画线程异常: {e}")

        # 中断已有动画线程
        if self._back_video_anim_thread and self._back_video_anim_thread.is_alive():
            self._back_video_anim_stop_event.set()
            self._back_video_anim_thread.join(timeout=1)
        self._back_video_anim_stop_event.clear()
        self._back_video_anim_thread = threading.Thread(target=anim_worker, daemon=True)
        self._back_video_anim_thread.start()
    
    def get_congrats_video_source_by_success_count(self, success_count: int) -> str:
        """根据抓中次数获取对应的祝贺视频源名，超出时用最后一个"""
        obs_cfg = self.config.get('obs', {})
        # 收集所有 congrats_video_sourceX
        sources = []
        i = 1
        while True:
            key = f'congrats_video_source{i}'
            if key in obs_cfg:
                sources.append(obs_cfg[key])
                i += 1
            else:
                break
        if not sources:
            # 兼容旧配置
            return obs_cfg.get('congrats_video_source', '')
        idx = min(success_count - 1, len(sources) - 1)
        return sources[idx]

    def show_congrats_by_success_count(self, player: str, item: str, success_count: int):
        """根据抓中次数显示对应祝贺视频源"""
        source_name = self.get_congrats_video_source_by_success_count(success_count)
        if source_name:
            logger.info(f"显示祝贺特效视频({success_count}次): {source_name}")
            self._show_source_with_auto_hide(source_name)
        else:
            logger.warning("未配置任何祝贺视频源，无法显示祝贺特效。")

    def show_failure_effect(self):
        """显示未抓中特效"""
        obs_cfg = self.config.get('obs', {})
        source_name = obs_cfg.get('missed_sound_source', '')
        if source_name:
            logger.info(f"显示未抓中特效: {source_name}")
            self._show_source_with_auto_hide(source_name)
        else:
            logger.warning("未配置未抓中特效源。")

    def _show_source_with_auto_hide(self, source_name: str):
        """显示指定源并在指定时间后自动隐藏"""
        if not self.connected:
            self.connect_to_obs()
            if not self.connected:
                return False
        lock_acquired = False
        try:
            lock_acquired = self.lock.acquire(timeout=3)
            if not lock_acquired:
                logger.warning("获取OBS锁超时，跳过特效显示")
                return False
            self._update_source_visibility(source_name, True)
            duration = self.congrats_duration
            def hide_after_delay():
                time.sleep(duration)
                self._update_source_visibility(source_name, False)
            threading.Thread(target=hide_after_delay, daemon=True).start()
            return True
        finally:
            if lock_acquired:
                self.lock.release()

    def update_config(self, new_obs_config: Dict[str, Any]):
        """更新OBS控制器的配置参数（线程安全）"""
        lock_acquired = False
        try:
            lock_acquired = self.lock.acquire(timeout=3)
            if not lock_acquired:
                logger.warning("获取OBS锁超时，跳过配置更新")
                return False

            # 更新可以运行时修改的配置参数
            if 'update_interval' in new_obs_config:
                self.update_interval = new_obs_config['update_interval']
                logger.info(f"OBS更新间隔已更新为: {self.update_interval}")

            if 'congrats_video_source' in new_obs_config:
                old_source = self.congrats_video_source
                self.congrats_video_source = new_obs_config['congrats_video_source']
                # 清除旧源的缓存
                if old_source in self.cached_scene_item_ids:
                    del self.cached_scene_item_ids[old_source]
                if old_source in self.visibility_states:
                    del self.visibility_states[old_source]
                logger.info(f"OBS祝贺视频源已更新为: {self.congrats_video_source}")

            if 'congrats_duration' in new_obs_config:
                self.congrats_duration = new_obs_config['congrats_duration']
                logger.info(f"OBS祝贺持续时间已更新为: {self.congrats_duration}")

            # 新增：热更新背景视频动画参数及开关
            for key in [
                'back_video_move_enabled',  # 新增
                'back_video_source', 'back_video_moveY', 'back_video_scale',
                'back_video_steps', 'back_video_movetime', 'back_video_dwell_time'
            ]:
                if key in new_obs_config:
                    self.config['obs'][key] = new_obs_config[key]
                    logger.info(f"OBS背景视频动画参数 {key} 已热更新为: {new_obs_config[key]}")

            # 新增：热更新祝贺视频源和未抓中特效源
            for i in range(1, 10):
                key = f'congrats_video_source{i}'
                if key in new_obs_config:
                    self.config['obs'][key] = new_obs_config[key]
            if 'missed_sound_source' in new_obs_config:
                self.config['obs']['missed_sound_source'] = new_obs_config['missed_sound_source']

            # 如果连接参数发生变化，需要重新连接（但这些参数在配置重新加载中被排除了）
            # 这里只是为了完整性而保留
            connection_changed = False
            if self.config and 'obs' in self.config:
                obs_config = self.config['obs']
                for key in ['host', 'port', 'password']:
                    if key in new_obs_config and new_obs_config[key] != obs_config.get(key):
                        connection_changed = True
                        break

            if connection_changed:
                logger.info("OBS连接参数发生变化，将重新连接")
                self.connected = False
                self._clear_cache()
                self.connect_to_obs()

            return True

        except Exception as e:
            logger.error(f"更新OBS配置时出错: {e}")
            return False
        finally:
            if lock_acquired:
                self.lock.release()

# 创建全局OBS控制器实例
obs_controller = None

def init_obs_controller(config=None):
    """初始化OBS控制器"""
    global obs_controller
    if obs_controller is None:
        logger.info("初始化全局 OBSController")
        obs_controller = OBSController(config)
    return obs_controller

def update_queue_display(in_que, player_info):
    """更新队列显示，供外部调用 - 此函数不再需要更新文本"""
    global obs_controller
    if obs_controller is None:
        logger.warning("obs_controller 未初始化!")
        return
    
    logger.debug("Global update_queue_display called, but OBS text display is disabled.")
